version: '3.7'
services:
  hyperf:
    image: hyperf/hyperf:8.1-alpine-v3.18-swoole
    environment:
      - "APP_PROJECT=fxerp"
      - "APP_ENV=test"
    ports:
      - "${HYPERF_HOST_PORT:-9501}:9501"
      - "${HYPERF_SWAGGER_PORT:-9503}:9503"
    volumes:
      - ./:/opt/www
    working_dir: /opt/www
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 5
      update_config:
        parallelism: 2
        delay: 5s
        order: start-first
